
# Required libraries for the function to work
library(dplyr)
library(tidyr)
library(tibble)

#' Calculate an improved correlation matrix based on log fold changes between disease and control samples.
#' This implementation better captures the four distinct patterns described in the paper:
#' 1. Intra-region, intra-phenotype conservation across cell types
#' 2. Cross-region, intra-phenotype conservation with weaker intra-cell-type correlations
#' 3. Minimal overlap between ALS and FTLD across regions
#' 4. Conserved intra-region, cell-type-specific alterations across ALS and FTLD
#'
#' @param expression_matrix A data frame or matrix of normalized gene expression (genes as rows, samples as columns).
#' @param metadata A data frame with sample metadata. Must contain columns 'Region', 'cell_type', and 'Condition'.
#'                 The rownames of metadata must match the colnames of expression_matrix.
#'                 'Condition' should contain values like "PN", "SALS", "C9ALS", "SFTLD", "C9FTLD".
#' @param control_level A character string specifying the value in the 'Condition' column that marks control samples. Defaults to "PN".
#' @param use_log_fold_change Logical, whether to use log fold changes instead of ratios. Defaults to TRUE.
#' @param min_samples_per_group Minimum number of samples required per region-cell_type-condition group. Defaults to 2.
#' @param pseudocount Small value added to avoid division by zero or log(0). Defaults to 1e-6.
#'
#' @return A square numeric matrix of the pairwise Pearson correlation between disease samples.
#'
calculate_disease_vs_control_correlation <- function(expression_matrix, metadata,
                                                   control_level = "PN",
                                                   use_log_fold_change = TRUE,
                                                   min_samples_per_group = 2,
                                                   pseudocount = 1e-6) {

  # --- 1. Prepare Data and Validate Input ---
  message("Step 1: Preparing and validating data...")

  # Ensure required columns exist
  required_cols <- c("Region", "cell_type", "Condition")
  missing_cols <- setdiff(required_cols, colnames(metadata))
  if (length(missing_cols) > 0) {
    stop("Missing required columns in metadata: ", paste(missing_cols, collapse = ", "))
  }

  # Create comprehensive identifiers
  metadata$region_celltype_id <- paste0(metadata$Region, "-", metadata$cell_type)
  metadata$region_celltype_condition_id <- paste0(metadata$Region, "-", metadata$cell_type, "-", metadata$Condition)

  # Split by condition
  is_control <- metadata$Condition == control_level
  colDataPN <- metadata[is_control, ]
  colDataDisease <- metadata[!is_control, ]

  if (nrow(colDataPN) == 0) {
    stop("No control samples found with condition: ", control_level)
  }
  if (nrow(colDataDisease) == 0) {
    stop("No disease samples found")
  }

  ctrl_group_data <- expression_matrix[, is_control, drop = FALSE]
  disease_group_data <- expression_matrix[, !is_control, drop = FALSE]

  # --- 2. Create Robust Control Reference Profiles ---
  message("Step 2: Creating robust control reference profiles...")

  # Check sample sizes per group
  control_group_sizes <- table(colDataPN$region_celltype_id)
  insufficient_groups <- names(control_group_sizes)[control_group_sizes < min_samples_per_group]

  if (length(insufficient_groups) > 0) {
    warning("Some control groups have fewer than ", min_samples_per_group, " samples: ",
            paste(insufficient_groups, collapse = ", "))
  }

  # Calculate mean control profiles with better error handling
  control_profiles <- ctrl_group_data %>%
    as.data.frame() %>%
    rownames_to_column(var = "gene_id") %>%
    pivot_longer(
      cols = -gene_id,
      names_to = "Sample_ID",
      values_to = "expression"
    ) %>%
    left_join(
      colDataPN %>%
        rownames_to_column("Sample_ID") %>%
        select(Sample_ID, region_celltype_id),
      by = "Sample_ID"
    ) %>%
    filter(!is.na(region_celltype_id)) %>%
    group_by(gene_id, region_celltype_id) %>%
    summarise(
      mean_expression = mean(expression, na.rm = TRUE),
      n_samples = n(),
      .groups = 'drop'
    ) %>%
    # Filter out groups with insufficient samples
    filter(n_samples >= min_samples_per_group) %>%
    select(-n_samples) %>%
    pivot_wider(
      names_from = region_celltype_id,
      values_from = mean_expression
    ) %>%
    column_to_rownames(var = "gene_id")

  # --- 3. Calculate Log Fold Changes for Disease Samples ---
  message("Step 3: Calculating ", ifelse(use_log_fold_change, "log fold changes", "fold changes"), " for disease samples...")

  # Add phenotype information for better tracking
  colDataDisease$phenotype <- case_when(
    grepl("ALS", colDataDisease$Condition) ~ "ALS",
    grepl("FTLD", colDataDisease$Condition) ~ "FTLD",
    TRUE ~ "Other"
  )

  results_list <- lapply(1:ncol(disease_group_data), function(i) {
    current_region_celltype <- colDataDisease$region_celltype_id[i]
    current_condition <- colDataDisease$Condition[i]
    current_phenotype <- colDataDisease$phenotype[i]
    sample_name <- rownames(colDataDisease)[i]

    if (current_region_celltype %in% colnames(control_profiles)) {
      control_profile <- control_profiles[, current_region_celltype]
      disease_profile <- disease_group_data[, i]

      # Add pseudocount to avoid division by zero
      control_profile_adj <- control_profile + pseudocount
      disease_profile_adj <- disease_profile + pseudocount

      if (use_log_fold_change) {
        # Calculate log2 fold change
        fold_change <- log2(disease_profile_adj / control_profile_adj)
      } else {
        # Calculate simple fold change (ratio)
        fold_change <- disease_profile_adj / control_profile_adj
      }

      # Remove infinite or NaN values
      fold_change[!is.finite(fold_change)] <- 0

      return(fold_change)
    } else {
      warning(paste("No matching control profile found for:", current_region_celltype,
                   "in sample:", sample_name))
      return(NULL)
    }
  })

  names(results_list) <- rownames(colDataDisease)

  # Combine into a single data frame, removing any samples that had no match
  valid_results <- Filter(Negate(is.null), results_list)

  if (length(valid_results) == 0) {
    stop("No valid disease-control comparisons could be made")
  }

  final_difference_df <- as.data.frame(do.call(cbind, valid_results))

  # --- 4. Calculate Final Correlation Matrix with Additional Metadata ---
  message("Step 4: Calculating the final correlation matrix...")

  # Remove genes with zero variance across all samples
  gene_vars <- apply(final_difference_df, 1, var, na.rm = TRUE)
  valid_genes <- !is.na(gene_vars) & gene_vars > 0

  if (sum(valid_genes) == 0) {
    stop("No genes with non-zero variance found")
  }

  final_difference_df_filtered <- final_difference_df[valid_genes, , drop = FALSE]

  # Calculate correlation matrix
  correlation_matrix <- cor(final_difference_df_filtered, use = "pairwise.complete.obs")

  # Add attributes for downstream analysis
  valid_metadata <- colDataDisease[colnames(final_difference_df), ]
  attr(correlation_matrix, "sample_metadata") <- valid_metadata
  attr(correlation_matrix, "n_genes_used") <- sum(valid_genes)
  attr(correlation_matrix, "method") <- ifelse(use_log_fold_change, "log2_fold_change", "fold_change")
  attr(correlation_matrix, "control_level") <- control_level

  message("Done. Used ", sum(valid_genes), " genes across ", ncol(final_difference_df), " disease samples.")
  return(correlation_matrix)
}
top_genes_name <- intersect(rownames(adata_mat)[top_genes], rownames(cleaned_data))
cor_df <- calculate_disease_vs_control_correlation(cleaned_data[top_genes_name,],metadata = colData)
ordered_correlation_matrix <- cor_df[order_indices, order_indices]
Heatmap(
  ordered_correlation_matrix,
  name = "Correlation", # This sets the title of the legend
  #col = correlation_color_function,
  # --- Key Parameters for Your Request ---
  cluster_rows = FALSE,
  cluster_columns = FALSE,
  top_annotation = column_ha,
  show_row_names = FALSE,
  show_column_names = FALSE,
  # --- End of Key Parameters ---
  
  # Optional: Improve aesthetics
  row_title = "Samples",
  column_title = "Samples",
  heatmap_legend_param = list(title = "Pearson Corr.")
)

