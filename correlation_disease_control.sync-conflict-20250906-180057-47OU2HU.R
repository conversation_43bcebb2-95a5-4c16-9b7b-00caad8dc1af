
# Required libraries for the function to work
library(dplyr)
library(tidyr)
library(tibble)

#' Calculate a correlation matrix based on difference scores between disease and control samples.
#'
#' @param expression_matrix A data frame or matrix of normalized gene expression (genes as rows, samples as columns).
#' @param metadata A data frame with sample metadata. Must contain columns 'Region', 'cell_type', and 'Condition'.
#'                 The rownames of metadata must match the colnames of expression_matrix.
#' @param control_level A character string specifying the value in the 'Condition' column that marks control samples. Defaults to "PN".
#'
#' @return A square numeric matrix of the pairwise Pearson correlation between disease samples.
#'
calculate_disease_vs_control_correlation <- function(expression_matrix, metadata, control_level = "PN") {
  
  # --- 1. Prepare Data and Split by Condition ---
  message("Step 1: Preparing and splitting data...")
  metadata$identify_id <- paste0(metadata$Region, "-", metadata$cell_type)
  
  is_control <- metadata$Condition == control_level
  colDataPN <- metadata[is_control, ]
  colDataDisease <- metadata[!is_control, ]
  
  ctrl_group_data <- expression_matrix[, is_control]
  disease_group_data <- expression_matrix[, !is_control]
  
  # --- 2. Aggregate Control Samples to Create Reference Profiles ---
  message("Step 2: Aggregating control samples to create reference profiles...")
  aggregated_data_tidy <- ctrl_group_data %>%
    as.data.frame() %>%
    rownames_to_column(var = "gene_id") %>%
    pivot_longer(
      cols = -gene_id,
      names_to = "Sample_ID",
      values_to = "expression"
    ) %>%
    mutate(identify_id = colDataPN$identify_id[match(Sample_ID, rownames(colDataPN))]) %>%
    group_by(gene_id, identify_id) %>%
    summarise(mean_expression = mean(expression, na.rm = TRUE), .groups = 'drop') %>%
    pivot_wider(
      names_from = identify_id,
      values_from = mean_expression
    ) %>%
    column_to_rownames(var = "gene_id")
  
  # --- 3. Calculate Difference Scores for Disease Samples ---
  message("Step 3: Calculating difference scores for each disease sample...")
  results_list <- lapply(1:ncol(disease_group_data), function(i) {
    current_identify_id <- colDataDisease$identify_id[i]
    if (current_identify_id %in% colnames(aggregated_data_tidy)) {
      control_profile <- aggregated_data_tidy[, current_identify_id]
      disease_profile <- disease_group_data[, i]
      return(disease_profile / control_profile)
    } else {
      # Warn the user if a matching control profile is not found
      warning(paste("No matching control profile found for identify_id:", current_identify_id))
      return(NULL)
    }
  })
  
  names(results_list) <- rownames(colDataDisease)
  
  # Combine into a single data frame, removing any samples that had no match
  final_difference_df <- as.data.frame(do.call(cbind, Filter(Negate(is.null), results_list)))
  
  # --- 4. Calculate Final Correlation Matrix ---
  message("Step 4: Calculating the final correlation matrix...")
  correlation_matrix <- cor(final_difference_df)
  
  message("Done.")
  return(correlation_matrix)
}
top_genes_name <- intersect(rownames(adata_mat)[top_genes], rownames(cleaned_data))
cor_df <- calculate_disease_vs_control_correlation(cleaned_data[top_genes_name,],metadata = colData)
ordered_correlation_matrix <- cor_df[order_indices, order_indices]
Heatmap(
  ordered_correlation_matrix,
  name = "Correlation", # This sets the title of the legend
  #col = correlation_color_function,
  # --- Key Parameters for Your Request ---
  cluster_rows = FALSE,
  cluster_columns = FALSE,
  top_annotation = column_ha,
  show_row_names = FALSE,
  show_column_names = FALSE,
  # --- End of Key Parameters ---
  
  # Optional: Improve aesthetics
  row_title = "Samples",
  column_title = "Samples",
  heatmap_legend_param = list(title = "Pearson Corr.")
)

